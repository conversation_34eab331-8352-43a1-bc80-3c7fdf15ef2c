-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:46:9-54:20
	android:grantUriPermissions
		ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:50:13-47
	android:authorities
		INJECTED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:48:13-60
	android:exported
		ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:49:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:47:13-62
manifest
ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:2:1-58:12
INJECTED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:2:1-58:12
INJECTED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:2:1-58:12
INJECTED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:2:1-58:12
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\ebf5f12d179771782bf41f123e88642d\transformed\navigation-common-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\55a18c243f4ac882d93d3a7cd9a80296\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\69f7b49adaa0dc41678f40a22d832f89\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\4fc72a0b32a789589bb4bfb0ac14697c\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.7.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\d916986582d5b8a927bbf5cebcb93f32\transformed\navigation-compose-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material3:material3-window-size-class-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f563ddd6287c157039d74db0ad1cb0e5\transformed\material3-window-size-class-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material3:material3-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\39e186d1df9a6e64a80555ea95166efd\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [io.coil-kt.coil3:coil-compose-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1523b0e2843b8c3b2206eec75aaa7434\transformed\coil-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt.coil3:coil-compose-core-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d83c8787f6bcf4edb35d0eda8a1527d\transformed\coil-compose-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\fcd20e835c8bf1213f25bc00fd020fc0\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d8d6e1aab7e26ebc2fe0647a99d7826\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aaf586443dc0b3325f20e0aca29bad67\transformed\animation\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\366abc712e29383e40ce99631f098d2f\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\78e087dd1cdd09d6432d260deb7bf0a8\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dce33176b030ff3f432a03fbfc15cda5\transformed\ui-tooling-data\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8d092bd3583207106842a78f0ce5e3e\transformed\ui-tooling-preview\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-unit-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10707ed4b16513a5d686adbcdd93d18f\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39ccfaa4b46a364def42306f3c4848c5\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6842abce5f5da049e25d4c5e979128a3\transformed\ui-geometry\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\df891fc547f4739d128cc2de1961a0ac\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb8e9ee106f64909a48113e968a603c5\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\77f3324e26b0fc7ae8260a8c77bc3ce8\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.material:material-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\01e4e2faf1698c602461ff28c59f0275\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\178ff5c54d6bc8a1dbeb66f819bb9e8e\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\56673a8b4479bfbc56575321e60b5dc5\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b6391c467cc8d37a1fceadaf9559aef0\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f834221be1c62a3e2d79d5a8d6b727f9\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\27eb01bccc439fad4c431fe11744a39c\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f9050601dae5b1dbcce96a44cee8f8ed\transformed\savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\08f0224dd2f3ac7f7ec5781649a0439f\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [io.coil-kt.coil3:coil-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8e8e35becc915fe2154275224e2d22d\transformed\coil-release\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt.coil3:coil-network-core-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a36a82e5ed0ac26ab8c79744fe18751c\transformed\coil-network-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt.coil3:coil-core-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8855094b50844372810db01026290202\transformed\coil-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0f5cff30a180d0faaad1f67045ce7477\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\32301300fe1d8bb38b9b0d036470e825\transformed\core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c2fe2863a9c50634a9253e6514925e3b\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\33314a664413fc190d8af7aeef943451\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f436669c1c0aa951dc18c727ff120d4\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bea41d8575f5b2c9f043a620a60b22ea\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a893d44f769554c05f2f9042f5c4ba3e\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d9770e3d771c462ed39db07884dbd01\transformed\emoji2-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7012401d2baa5baca522f517fb27a57e\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5127fbf4aa18facdfc5d332b39b8229\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0c42c46107fd561085aaed561c4cf813\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\db62b0c2343926056a7d6419eca539f7\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\58eeb28f86695ac6888d7afb81f7d4a5\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\416368b85810fee672d1adc3adb76197\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\b64d6959e2736c03e82d34c3fee8f775\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\57c8256c9aa49c73e70860005aae44e1\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.37.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\4295243598a600d6392f4746f1b5c8dd\transformed\accompanist-drawablepainter-0.37.3\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.compose.ui:ui-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5d97d34d84112940f02348422c3753d\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\54a6656068f8f5c44960de6cc4931323\transformed\ui-test-manifest-1.8.0\AndroidManifest.xml:17:1-29:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\eae94b96c36613b201ce2c6cc3fa87e3\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cb287a452abedb26e4c003e4e8fe9db\transformed\activity-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d93cf781d3eb9d67a9f1152cdf6b002\transformed\activity-compose-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac027c6e0e0240bf4974a08c7329219d\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\44e7bd1b86c32aef1e8297894b9d3141\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44dfff852f8446f00028c43f19613595\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1ff346b9db1ba5563c0e48712d638e7\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d19ceabcd00b3e0d82ee2aacc706da1\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bbc5f0111a2902138f99cccb9f2b8de1\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\13a3bbc9656c8aa176373f0021fcd2e2\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a0f98d4ba2f333920fc08d582ef6821\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aae976a0f67053f6b2d2ba71dceadfc4\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b027bcfc2bbe3124197624f8b76ed37c\transformed\exifinterface-1.4.1\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:2:11-69
	tools:ignore
		ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:4:5-29
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:6:5-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.RECORD_AUDIO
ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:7:5-71
	android:name
		ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:7:22-68
uses-permission#android.permission.CAMERA
ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:8:5-65
	android:name
		ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:8:22-62
uses-feature#android.hardware.camera
ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:9:5-85
	android:required
		ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:9:58-82
	android:name
		ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:9:19-57
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:11:5-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:11:22-73
uses-permission#android.permission.READ_MEDIA_VISUAL_USER_SELECTED
ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:14:5-90
	android:name
		ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:14:22-87
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:17:5-107
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:17:78-104
	android:name
		ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:17:22-77
application
ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:19:5-56:19
INJECTED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:19:5-56:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\77f3324e26b0fc7ae8260a8c77bc3ce8\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\77f3324e26b0fc7ae8260a8c77bc3ce8\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c2fe2863a9c50634a9253e6514925e3b\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c2fe2863a9c50634a9253e6514925e3b\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d9770e3d771c462ed39db07884dbd01\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d9770e3d771c462ed39db07884dbd01\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5127fbf4aa18facdfc5d332b39b8229\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5127fbf4aa18facdfc5d332b39b8229\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\58eeb28f86695ac6888d7afb81f7d4a5\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\58eeb28f86695ac6888d7afb81f7d4a5\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\54a6656068f8f5c44960de6cc4931323\transformed\ui-test-manifest-1.8.0\AndroidManifest.xml:22:5-27:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\54a6656068f8f5c44960de6cc4931323\transformed\ui-test-manifest-1.8.0\AndroidManifest.xml:22:5-27:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d19ceabcd00b3e0d82ee2aacc706da1\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d19ceabcd00b3e0d82ee2aacc706da1\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a0f98d4ba2f333920fc08d582ef6821\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a0f98d4ba2f333920fc08d582ef6821\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml
	android:roundIcon
		ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:28:9-54
	android:icon
		ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:23:9-43
	android:networkSecurityConfig
		ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:27:9-69
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5127fbf4aa18facdfc5d332b39b8229\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:29:9-35
	android:label
		ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:24:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:22:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:31:9-29
	android:allowBackup
		ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:20:9-35
	android:theme
		ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:30:9-42
	android:enableOnBackInvokedCallback
		ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:25:9-51
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:21:9-65
	android:usesCleartextTraffic
		ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:26:9-44
activity#com.example.everytalk.statecontroller.MainActivity
ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:33:9-44:20
	android:windowSoftInputMode
		ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:37:13-55
	android:exported
		ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:35:13-36
	tools:ignore
		ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:38:13-47
	android:theme
		ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:36:13-46
	android:name
		ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:34:13-78
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:39:13-43:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:40:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:40:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:42:17-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:42:27-74
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:51:13-53:54
	android:resource
		ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:53:17-51
	android:name
		ADDED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:52:17-67
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\ebf5f12d179771782bf41f123e88642d\transformed\navigation-common-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\ebf5f12d179771782bf41f123e88642d\transformed\navigation-common-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\55a18c243f4ac882d93d3a7cd9a80296\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\55a18c243f4ac882d93d3a7cd9a80296\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\69f7b49adaa0dc41678f40a22d832f89\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\69f7b49adaa0dc41678f40a22d832f89\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\4fc72a0b32a789589bb4bfb0ac14697c\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\4fc72a0b32a789589bb4bfb0ac14697c\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\d916986582d5b8a927bbf5cebcb93f32\transformed\navigation-compose-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\d916986582d5b8a927bbf5cebcb93f32\transformed\navigation-compose-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-window-size-class-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f563ddd6287c157039d74db0ad1cb0e5\transformed\material3-window-size-class-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-window-size-class-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f563ddd6287c157039d74db0ad1cb0e5\transformed\material3-window-size-class-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\39e186d1df9a6e64a80555ea95166efd\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\39e186d1df9a6e64a80555ea95166efd\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [io.coil-kt.coil3:coil-compose-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1523b0e2843b8c3b2206eec75aaa7434\transformed\coil-compose-release\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt.coil3:coil-compose-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1523b0e2843b8c3b2206eec75aaa7434\transformed\coil-compose-release\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt.coil3:coil-compose-core-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d83c8787f6bcf4edb35d0eda8a1527d\transformed\coil-compose-core-release\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt.coil3:coil-compose-core-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d83c8787f6bcf4edb35d0eda8a1527d\transformed\coil-compose-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\fcd20e835c8bf1213f25bc00fd020fc0\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\fcd20e835c8bf1213f25bc00fd020fc0\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d8d6e1aab7e26ebc2fe0647a99d7826\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d8d6e1aab7e26ebc2fe0647a99d7826\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aaf586443dc0b3325f20e0aca29bad67\transformed\animation\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aaf586443dc0b3325f20e0aca29bad67\transformed\animation\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\366abc712e29383e40ce99631f098d2f\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\366abc712e29383e40ce99631f098d2f\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\78e087dd1cdd09d6432d260deb7bf0a8\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\78e087dd1cdd09d6432d260deb7bf0a8\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dce33176b030ff3f432a03fbfc15cda5\transformed\ui-tooling-data\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dce33176b030ff3f432a03fbfc15cda5\transformed\ui-tooling-data\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8d092bd3583207106842a78f0ce5e3e\transformed\ui-tooling-preview\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8d092bd3583207106842a78f0ce5e3e\transformed\ui-tooling-preview\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10707ed4b16513a5d686adbcdd93d18f\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10707ed4b16513a5d686adbcdd93d18f\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39ccfaa4b46a364def42306f3c4848c5\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39ccfaa4b46a364def42306f3c4848c5\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6842abce5f5da049e25d4c5e979128a3\transformed\ui-geometry\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6842abce5f5da049e25d4c5e979128a3\transformed\ui-geometry\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\df891fc547f4739d128cc2de1961a0ac\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\df891fc547f4739d128cc2de1961a0ac\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb8e9ee106f64909a48113e968a603c5\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb8e9ee106f64909a48113e968a603c5\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\77f3324e26b0fc7ae8260a8c77bc3ce8\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\77f3324e26b0fc7ae8260a8c77bc3ce8\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\01e4e2faf1698c602461ff28c59f0275\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\01e4e2faf1698c602461ff28c59f0275\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\178ff5c54d6bc8a1dbeb66f819bb9e8e\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\178ff5c54d6bc8a1dbeb66f819bb9e8e\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\56673a8b4479bfbc56575321e60b5dc5\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\56673a8b4479bfbc56575321e60b5dc5\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b6391c467cc8d37a1fceadaf9559aef0\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b6391c467cc8d37a1fceadaf9559aef0\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f834221be1c62a3e2d79d5a8d6b727f9\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f834221be1c62a3e2d79d5a8d6b727f9\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\27eb01bccc439fad4c431fe11744a39c\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\27eb01bccc439fad4c431fe11744a39c\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f9050601dae5b1dbcce96a44cee8f8ed\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f9050601dae5b1dbcce96a44cee8f8ed\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\08f0224dd2f3ac7f7ec5781649a0439f\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\08f0224dd2f3ac7f7ec5781649a0439f\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [io.coil-kt.coil3:coil-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8e8e35becc915fe2154275224e2d22d\transformed\coil-release\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt.coil3:coil-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8e8e35becc915fe2154275224e2d22d\transformed\coil-release\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt.coil3:coil-network-core-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a36a82e5ed0ac26ab8c79744fe18751c\transformed\coil-network-core-release\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt.coil3:coil-network-core-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a36a82e5ed0ac26ab8c79744fe18751c\transformed\coil-network-core-release\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt.coil3:coil-core-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8855094b50844372810db01026290202\transformed\coil-core-release\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt.coil3:coil-core-android:3.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8855094b50844372810db01026290202\transformed\coil-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0f5cff30a180d0faaad1f67045ce7477\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0f5cff30a180d0faaad1f67045ce7477\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\32301300fe1d8bb38b9b0d036470e825\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\32301300fe1d8bb38b9b0d036470e825\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c2fe2863a9c50634a9253e6514925e3b\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c2fe2863a9c50634a9253e6514925e3b\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\33314a664413fc190d8af7aeef943451\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\33314a664413fc190d8af7aeef943451\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f436669c1c0aa951dc18c727ff120d4\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f436669c1c0aa951dc18c727ff120d4\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bea41d8575f5b2c9f043a620a60b22ea\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bea41d8575f5b2c9f043a620a60b22ea\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a893d44f769554c05f2f9042f5c4ba3e\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a893d44f769554c05f2f9042f5c4ba3e\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d9770e3d771c462ed39db07884dbd01\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d9770e3d771c462ed39db07884dbd01\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7012401d2baa5baca522f517fb27a57e\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7012401d2baa5baca522f517fb27a57e\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5127fbf4aa18facdfc5d332b39b8229\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5127fbf4aa18facdfc5d332b39b8229\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0c42c46107fd561085aaed561c4cf813\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0c42c46107fd561085aaed561c4cf813\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\db62b0c2343926056a7d6419eca539f7\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\db62b0c2343926056a7d6419eca539f7\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\58eeb28f86695ac6888d7afb81f7d4a5\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\58eeb28f86695ac6888d7afb81f7d4a5\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\416368b85810fee672d1adc3adb76197\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\416368b85810fee672d1adc3adb76197\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\b64d6959e2736c03e82d34c3fee8f775\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\b64d6959e2736c03e82d34c3fee8f775\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\57c8256c9aa49c73e70860005aae44e1\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\57c8256c9aa49c73e70860005aae44e1\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.37.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\4295243598a600d6392f4746f1b5c8dd\transformed\accompanist-drawablepainter-0.37.3\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.37.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\4295243598a600d6392f4746f1b5c8dd\transformed\accompanist-drawablepainter-0.37.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5d97d34d84112940f02348422c3753d\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5d97d34d84112940f02348422c3753d\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\54a6656068f8f5c44960de6cc4931323\transformed\ui-test-manifest-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\54a6656068f8f5c44960de6cc4931323\transformed\ui-test-manifest-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\eae94b96c36613b201ce2c6cc3fa87e3\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\eae94b96c36613b201ce2c6cc3fa87e3\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cb287a452abedb26e4c003e4e8fe9db\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cb287a452abedb26e4c003e4e8fe9db\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d93cf781d3eb9d67a9f1152cdf6b002\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d93cf781d3eb9d67a9f1152cdf6b002\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac027c6e0e0240bf4974a08c7329219d\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac027c6e0e0240bf4974a08c7329219d\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\44e7bd1b86c32aef1e8297894b9d3141\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\44e7bd1b86c32aef1e8297894b9d3141\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44dfff852f8446f00028c43f19613595\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44dfff852f8446f00028c43f19613595\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1ff346b9db1ba5563c0e48712d638e7\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1ff346b9db1ba5563c0e48712d638e7\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d19ceabcd00b3e0d82ee2aacc706da1\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d19ceabcd00b3e0d82ee2aacc706da1\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bbc5f0111a2902138f99cccb9f2b8de1\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bbc5f0111a2902138f99cccb9f2b8de1\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\13a3bbc9656c8aa176373f0021fcd2e2\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\13a3bbc9656c8aa176373f0021fcd2e2\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a0f98d4ba2f333920fc08d582ef6821\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a0f98d4ba2f333920fc08d582ef6821\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aae976a0f67053f6b2d2ba71dceadfc4\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aae976a0f67053f6b2d2ba71dceadfc4\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b027bcfc2bbe3124197624f8b76ed37c\transformed\exifinterface-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.exifinterface:exifinterface:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b027bcfc2bbe3124197624f8b76ed37c\transformed\exifinterface-1.4.1\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\77f3324e26b0fc7ae8260a8c77bc3ce8\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\77f3324e26b0fc7ae8260a8c77bc3ce8\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\77f3324e26b0fc7ae8260a8c77bc3ce8\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c2fe2863a9c50634a9253e6514925e3b\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c2fe2863a9c50634a9253e6514925e3b\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c2fe2863a9c50634a9253e6514925e3b\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c2fe2863a9c50634a9253e6514925e3b\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c2fe2863a9c50634a9253e6514925e3b\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c2fe2863a9c50634a9253e6514925e3b\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d9770e3d771c462ed39db07884dbd01\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\58eeb28f86695ac6888d7afb81f7d4a5\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\58eeb28f86695ac6888d7afb81f7d4a5\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d19ceabcd00b3e0d82ee2aacc706da1\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d19ceabcd00b3e0d82ee2aacc706da1\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d9770e3d771c462ed39db07884dbd01\transformed\emoji2-1.4.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d9770e3d771c462ed39db07884dbd01\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d9770e3d771c462ed39db07884dbd01\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d9770e3d771c462ed39db07884dbd01\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d9770e3d771c462ed39db07884dbd01\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d9770e3d771c462ed39db07884dbd01\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d9770e3d771c462ed39db07884dbd01\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5127fbf4aa18facdfc5d332b39b8229\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5127fbf4aa18facdfc5d332b39b8229\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5127fbf4aa18facdfc5d332b39b8229\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#com.example.everytalk.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5127fbf4aa18facdfc5d332b39b8229\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5127fbf4aa18facdfc5d332b39b8229\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5127fbf4aa18facdfc5d332b39b8229\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5127fbf4aa18facdfc5d332b39b8229\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5127fbf4aa18facdfc5d332b39b8229\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#com.example.everytalk.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5127fbf4aa18facdfc5d332b39b8229\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5127fbf4aa18facdfc5d332b39b8229\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\58eeb28f86695ac6888d7afb81f7d4a5\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\58eeb28f86695ac6888d7afb81f7d4a5\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\58eeb28f86695ac6888d7afb81f7d4a5\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\54a6656068f8f5c44960de6cc4931323\transformed\ui-test-manifest-1.8.0\AndroidManifest.xml:23:9-26:79
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\54a6656068f8f5c44960de6cc4931323\transformed\ui-test-manifest-1.8.0\AndroidManifest.xml:25:13-36
	android:theme
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\54a6656068f8f5c44960de6cc4931323\transformed\ui-test-manifest-1.8.0\AndroidManifest.xml:26:13-76
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\54a6656068f8f5c44960de6cc4931323\transformed\ui-test-manifest-1.8.0\AndroidManifest.xml:24:13-63
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:50:25-92
