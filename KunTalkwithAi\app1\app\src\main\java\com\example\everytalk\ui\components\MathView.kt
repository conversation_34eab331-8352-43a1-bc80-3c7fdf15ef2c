package com.example.everytalk.ui.components

import android.annotation.SuppressLint
import android.util.Log
import android.view.MotionEvent
import android.webkit.ConsoleMessage
import android.webkit.WebChromeClient
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import java.util.concurrent.ConcurrentHashMap

// Cache for pre-rendered KaTeX HTML to avoid re-rendering
private val htmlCache = ConcurrentHashMap<String, String>()

@SuppressLint("SetJavaScriptEnabled")
@Composable
fun MathView(
    latex: String,
    isDisplay: Boolean,
    textColor: Color,
    modifier: Modifier = Modifier,
) {
    val context = LocalContext.current
    var webViewHeight by remember { mutableStateOf(if (isDisplay) 60.dp else 40.dp) }
    val cacheKey = "$latex-$isDisplay-${textColor.value}"

    // Generate the HTML content for KaTeX
    val htmlContent = remember(cacheKey) {
        htmlCache.getOrPut(cacheKey) {
            val displayStyle = if (isDisplay) "block" else "inline"
            val hexColor = String.format(
                "#%02x%02x%02x%02x",
                (textColor.alpha * 255).toInt(),
                (textColor.red * 255).toInt(),
                (textColor.green * 255).toInt(),
                (textColor.blue * 255).toInt()
            )

            """
            <!DOCTYPE html>
            <html>
            <head>
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.0/dist/katex.min.css">
                <script src="https://cdn.jsdelivr.net/npm/katex@0.16.0/dist/katex.min.js"></script>
                <style>
                    * {
                        margin: 0;
                        padding: 0;
                        box-sizing: border-box;
                    }
                    html, body {
                        width: 100%;
                        height: 100%;
                        overflow-x: auto;
                        overflow-y: hidden;
                        background-color: transparent;
                    }
                    body {
                        padding: 8px;
                        font-size: 16px;
                        color: $hexColor;
                        display: flex;
                        align-items: center;
                        justify-content: flex-start;
                        min-width: 100%;
                        white-space: nowrap;
                    }
                    #math {
                        display: inline-block;
                        white-space: nowrap;
                        min-width: max-content;
                    }
                    .katex {
                        font-size: 1em;
                        white-space: nowrap;
                        display: inline-block;
                    }
                    .katex-display {
                        margin: 0;
                        white-space: nowrap;
                        display: inline-block;
                        font-size: 1.2em;
                    }
                </style>
            </head>
            <body>
                <div id="math"></div>
                <script>
                    try {
                        katex.render(`${latex.replace("\\", "\\\\")}`, document.getElementById('math'), {
                            throwOnError: false,
                            displayMode: $isDisplay
                        });
                        
                        // Wait for rendering to complete, then notify height and enable scrolling
                        setTimeout(() => {
                            const mathElement = document.getElementById('math');
                            const body = document.body;
                            
                            // Calculate the actual content dimensions with more generous padding
                            const height = Math.max(body.scrollHeight, mathElement.offsetHeight + 40);
                            const width = mathElement.scrollWidth;
                            
                            // Set minimum width to ensure scrolling works
                            if (width > window.innerWidth) {
                                body.style.minWidth = width + 'px';
                                mathElement.style.minWidth = width + 'px';
                            }
                            
                            if (window.android) {
                                window.android.updateHeight(height);
                            }
                            
                            console.log('Math rendered - Height:', height, 'Width:', width, 'Window width:', window.innerWidth);
                        }, 150);
                    } catch (e) {
                        console.error('KaTeX render error:', e);
                        document.getElementById('math').innerText = e.toString();
                        if (window.android) {
                            window.android.updateHeight(60);
                        }
                    }
                </script>
            </body>
            </html>
            """.trimIndent()
        }
    }

    BoxWithConstraints(modifier = modifier) {
        AndroidView(
            factory = {
                val webView = object : WebView(it) {
                    override fun onTouchEvent(event: MotionEvent?): Boolean {
                        // Always allow touch events for scrolling
                        return super.onTouchEvent(event)
                    }
                    
                    override fun onScrollChanged(l: Int, t: Int, oldl: Int, oldt: Int) {
                        // Allow horizontal scrolling but prevent vertical scrolling
                        if (t != 0) {
                            scrollTo(l, 0)
                        } else {
                            super.onScrollChanged(l, t, oldl, oldt)
                        }
                    }
                }.apply {
                    settings.javaScriptEnabled = true
                    settings.loadWithOverviewMode = true
                    settings.useWideViewPort = true
                    settings.setSupportZoom(false)
                    settings.builtInZoomControls = false
                    settings.displayZoomControls = false
                    setBackgroundColor(0) // Transparent background
                    
                    // Enable horizontal scrolling
                    isHorizontalScrollBarEnabled = true
                    isVerticalScrollBarEnabled = false

                    // Add logging for debugging
                    webChromeClient = object : WebChromeClient() {
                        override fun onConsoleMessage(consoleMessage: ConsoleMessage): Boolean {
                            Log.d("MathView", "${consoleMessage.message()} -- From line ${consoleMessage.lineNumber()} of ${consoleMessage.sourceId()}")
                            return super.onConsoleMessage(consoleMessage)
                        }
                    }

                    webViewClient = object : WebViewClient() {
                        override fun onPageFinished(view: WebView?, url: String?) {
                            super.onPageFinished(view, url)
                            Log.d("MathView", "Page finished loading: $url")
                        }
                    }
                    
                    addJavascriptInterface(object {
                        @android.webkit.JavascriptInterface
                        fun updateHeight(newHeight: Int) {
                            // Update height based on content, add generous padding for math formulas
                            val density = resources.displayMetrics.density
                            val calculatedHeight = (newHeight / density).dp + 16.dp
                            // Always update height to ensure proper display
                            webViewHeight = maxOf(calculatedHeight, if (isDisplay) 60.dp else 40.dp)
                            Log.d("MathView", "New height from JS: $newHeight -> $webViewHeight")
                        }
                    }, "android")
                }
                webView
            },
            update = { webView ->
                webView.loadDataWithBaseURL(null, htmlContent, "text/html", "UTF-8", null)
            },
            modifier = Modifier.height(webViewHeight).fillMaxWidth()
        )
    }
}