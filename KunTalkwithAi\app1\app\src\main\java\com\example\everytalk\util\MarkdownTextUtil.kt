package com.example.everytalk.util

import androidx.compose.ui.graphics.Color
import java.util.regex.Pattern

// Sealed interface for different types of content blocks
sealed interface ContentBlock {
    data class TextBlock(val content: String) : ContentBlock
    data class MathBlock(val latex: String, val isDisplay: Boolean) : ContentBlock
    data class CodeBlock(val code: String, val language: String?) : ContentBlock
}

fun parseToContentBlocks(markdown: String): List<ContentBlock> {
    // Regex to find code blocks (```...```), block math ($$...$$), and inline math ($...$)
    val pattern = Pattern.compile("(?s)(```(.*?)```|\\$\\$(.*?)\\$\\$|(?<!\\\\)\\$(.*?)(?<!\\\\)\\$)")
    val matcher = pattern.matcher(markdown)
    val blocks = mutableListOf<ContentBlock>()
    var lastIndex = 0

    while (matcher.find()) {
        // Add the text part before the match
        if (matcher.start() > lastIndex) {
            blocks.add(ContentBlock.TextBlock(markdown.substring(lastIndex, matcher.start())))
        }

        // Determine the type of the matched block
        val codeMatch = matcher.group(2)
        val blockMathMatch = matcher.group(3)
        val inlineMathMatch = matcher.group(4)

        when {
            codeMatch != null -> {
                val lines = codeMatch.trim().lines()
                val language = lines.firstOrNull()?.takeIf { it.isNotBlank() }
                val code = if (language != null) lines.drop(1).joinToString("\n") else codeMatch.trim()
                blocks.add(ContentBlock.CodeBlock(code, language))
            }
            blockMathMatch != null -> blocks.add(ContentBlock.MathBlock(blockMathMatch.trim(), isDisplay = true))
            inlineMathMatch != null -> blocks.add(ContentBlock.MathBlock(inlineMathMatch.trim(), isDisplay = false))
        }
        lastIndex = matcher.end()
    }

    // Add the remaining text part after the last match
    if (lastIndex < markdown.length) {
        blocks.add(ContentBlock.TextBlock(markdown.substring(lastIndex)))
    }

    return blocks
}