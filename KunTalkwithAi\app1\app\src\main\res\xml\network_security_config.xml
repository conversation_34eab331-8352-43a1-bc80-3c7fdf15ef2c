<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <!-- 允许所有HTTP流量 -->
    <base-config cleartextTrafficPermitted="true">
        <trust-anchors>
            <certificates src="system"/>
        </trust-anchors>
    </base-config>
    
    <!-- 特定域名配置，支持常见的局域网地址和开发地址 -->
    <domain-config cleartextTrafficPermitted="true">
        <!-- 本地地址 -->
        <domain includeSubdomains="true">localhost</domain>
        <domain includeSubdomains="true">127.0.0.1</domain>
        <domain includeSubdomains="true">********</domain>
        
        <!-- 常见局域网地址段 -->
        <domain includeSubdomains="true">***********</domain>
        <domain includeSubdomains="true">***********00</domain>
        <domain includeSubdomains="true">***********01</domain>
        <domain includeSubdomains="true">*************</domain>
        <domain includeSubdomains="true">***********03</domain>
        <domain includeSubdomains="true">***********04</domain>
        <domain includeSubdomains="true">***********05</domain>
        <domain includeSubdomains="true">***********</domain>
        <domain includeSubdomains="true">********</domain>
        <domain includeSubdomains="true">**********</domain>
        
        <!-- 支持任何IP地址模式（通配符方式） -->
        <!-- 注意：这种方式可能不被所有版本支持，所以我们列出常见的地址 -->
    </domain-config>
</network-security-config>